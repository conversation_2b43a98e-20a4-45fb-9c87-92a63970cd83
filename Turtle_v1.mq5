//+------------------------------------------------------------------+
//|                                                    Turtle_V1.mq5 |
//|                                                               CG |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "CG"
#property link      ""
#property version   "1.00"

#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Indicators/Indicators.mqh>

#define REAL_TRADE

#ifdef REAL_TRADE
input string API_KEY    = "dein_key";
input string API_SECRET = "dein_secret";
string BASE_URL         = "https://api.bybit.com";
#endif

input int      EntryPeriodShort   = 20;     // 20-Tage-Breakout (System-1)
input int      EntryPeriodLong    = 55;     // 55-Tage-Breakout (System-2)
input int      ExitPeriodShort    = 10;     // 10-Tage-Exit (System-1)
input int      ExitPeriodLong     = 20;     // 20-Tage-Exit (System-2)
input int      ATRPeriod          = 20;     // ATR-Periode (N)
input double   RiskPercent        = 0.5;    // Risiko je Einheit (% des Saldos)
input int      MaxUnits           = 4;      // Max. Pyramiding-Einheiten
input double   PyramidFactor      = 0.5;    // Abstand (xN) pro zusätzl. Einheit
input double   StopMul            = 2.0;    // Stop-Loss-Faktor (xN)
input bool     AllowLong          = true;   // Long-Trades erlauben
input bool     AllowShort         = true;   // Short-Trades erlauben
input ENUM_TIMEFRAMES TF         = PERIOD_D1; // Analyse-Timeframe
input uint     Slippage           = 20;     // Slippage (Punkte)
input ulong    Magic              = 198304; // Magic-Number

CTrade trade;
CPositionInfo position;
double g_atr = 0.0;
int    g_UnitsOpened = 0;
bool   g_LastTradeLoss = false;
datetime lastBar = 0;

//--- HMAC SHA256
/*string HmacSHA256(string msg, string key)
{
   uchar k[], d[], out[];
   StringToCharArray(key, k);
   StringToCharArray(msg, d);
   CryptEncode(CRYPT_HASH_SHA256, d, k, out);  // ← HMAC
   return CharArrayToString(out);
}*/

#ifdef REAL_TRADE
// Funktion: Berechnet HMAC-SHA256 und gibt Hex-String zurück
string HmacSHA256(string data, string key)
{
   uchar dataArr[], keyArr[], outArr[];

   StringToCharArray(data, dataArr);
   StringToCharArray(key, keyArr);

   if(!CryptEncode(CRYPT_HASH_SHA256, dataArr, keyArr, outArr))
   {
      Print("Fehler bei HMAC-SHA256");
      return "";
   }

   // In Hex-String umwandeln (optional: Base64)
   string hexResult = "";
   for (int i = 0; i < ArraySize(outArr); i++)
      hexResult += StringFormat("%02X", outArr[i]);

   return StringToLower(hexResult);  // oder original lassen
}

//--- REST POST für Order
bool BybitOrder(string symbol, string side, double qty)
{
   string ts = (string)((long)TimeCurrent() * 1000);  // Millisekunden
   string body = "{\"category\":\"linear\",\"symbol\":\""+symbol+
                 "\",\"side\":\""+side+"\",\"orderType\":\"Market\",\"qty\":\""+
                 DoubleToString(qty, 3)+"\"}";
   string pre_sign = ts + API_KEY + "5000" + body;
   string signature = HmacSHA256(pre_sign, API_SECRET);

   string headers = "Content-Type: application/json\r\n" +
                    "X-BAPI-API-KEY: " + API_KEY + "\r\n" +
                    "X-BAPI-SIGN: " + signature + "\r\n" +
                    "X-BAPI-TIMESTAMP: " + ts + "\r\n" +
                    "X-BAPI-RECV-WINDOW: 5000";

   uchar bodyArr[]; StringToCharArray(body, bodyArr);
   uchar resArr[];  string response_headers;
   int code = WebRequest("POST", BASE_URL + "/v5/order/create", headers, 10000, bodyArr, resArr, response_headers);
   Print("HTTP Response: ", CharArrayToString(resArr));
   return code == 200;
}
#endif

//--- ATR Berechnung
bool GetATR()
{
   double atrBuffer[];
   if(CopyBuffer(iATR(_Symbol, TF, ATRPeriod), 0, 0, 1, atrBuffer)==1)
   {
      g_atr = atrBuffer[0];
      return (g_atr > 0);
   }
   return false;
}

//--- Positionsgröße berechnen
double CalcUnitLots(bool buy)
{
   if(g_atr <= 0) return 0;
   double riskMoney = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercent / 100.0;
   Print("SYMBOL:", _Symbol);
   double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
   double currentPrice = (buy) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   Print("Contract-Size:", contractSize, " Current-Price:", currentPrice);
   double lots = riskMoney / (currentPrice * contractSize);
   Print("-> needed lots:", lots);

   double step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   Print("Symbol step-size:", step);
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   Print("Symbol min. Lotsize:", minLot);
   lots = MathFloor(lots / step) * step;
   return (lots < minLot) ? minLot : lots;
}

//--- Breakout Levels
void GetBreakoutLevels(int period, double &highest, double &lowest)
{
   MqlRates rates[];
   if(CopyRates(_Symbol, TF, 2, period, rates) == period)
   {
      highest = rates[0].high;
      lowest = rates[0].low;
      for(int i=1; i<period; i++)
      {
         if(rates[i].high > highest) highest = rates[i].high;
         if(rates[i].low  < lowest ) lowest  = rates[i].low;
      }
   }
}

//--- Einstieg
void TryEntry()
{
   double eHigh20, eLow20, eHigh55, eLow55;
   GetBreakoutLevels(EntryPeriodShort, eHigh20, eLow20);
   GetBreakoutLevels(EntryPeriodLong, eHigh55, eLow55);

   double buylots = CalcUnitLots(true);
   double selllots = CalcUnitLots(false);
   Print("-> real lot count (buy):", buylots," sell:",selllots);
   if(buylots <= 0 || selllots <= 0) return;
   // Stop-Loss vorberechnen
   double newBuySL = SymbolInfoDouble(_Symbol, SYMBOL_BID) - StopMul*g_atr;
   double newSellSL = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + StopMul*g_atr;
   Print("=> Price (buy):", SymbolInfoDouble(_Symbol, SYMBOL_BID), "-> new SL (buy):", newBuySL);
   Print("=> Price (sell):", SymbolInfoDouble(_Symbol, SYMBOL_ASK), "-> new SL (sell):", newSellSL);

   if(g_LastTradeLoss)
   {
      if(AllowLong  && SymbolInfoDouble(_Symbol, SYMBOL_BID) > eHigh20)
         trade.Buy(buylots, _Symbol, 0, newBuySL, 0, "Turtle");
      if(AllowShort && SymbolInfoDouble(_Symbol, SYMBOL_ASK) < eLow20)
         trade.Sell(selllots, _Symbol, 0, newSellSL, 0, "Turtle");
   }
   else
   {
      if(AllowLong  && SymbolInfoDouble(_Symbol, SYMBOL_BID) > eHigh55)
         trade.Buy(buylots, _Symbol, 0, newBuySL, 0, "Turtle");
      if(AllowShort && SymbolInfoDouble(_Symbol, SYMBOL_ASK) < eLow55)
         trade.Sell(selllots, _Symbol, 0, newSellSL, 0, "Turtle");
   }
}

//--- Exit
void TryExit()
{
   double exitHigh10, exitLow10, exitHigh20, exitLow20;
   GetBreakoutLevels(ExitPeriodShort, exitHigh10, exitLow10);
   GetBreakoutLevels(ExitPeriodLong, exitHigh20, exitLow20);

   if(position.Select(_Symbol))
   {
      double price = SymbolInfoDouble(_Symbol, (position.PositionType()==POSITION_TYPE_BUY) ? SYMBOL_BID : SYMBOL_ASK);
      bool close = false;

      if(position.PositionType() == POSITION_TYPE_BUY)
         close = (price < exitLow10 || price < exitLow20);
      else
         close = (price > exitHigh10 || price > exitHigh20);

      if(close)
      {
         double plBefore = AccountInfoDouble(ACCOUNT_PROFIT);
#ifdef REAL_TRADE
         BybitOrder(_Symbol,)
#else
         trade.PositionClose(_Symbol);
#endif
         double plAfter = AccountInfoDouble(ACCOUNT_PROFIT);
         g_LastTradeLoss = (plAfter - plBefore) < 0;
         g_UnitsOpened = 0;
      }
   }
}

//--- Pyramiding + Stop
void ManagePosition()
{
   if(!position.Select(_Symbol)) return;

   ENUM_POSITION_TYPE type = position.PositionType();
   double avgPrice = position.PriceOpen();
   double priceNow = (type == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID)
                                                 : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   // Pyramiding
   if(g_UnitsOpened < MaxUnits)
   {
      double nextLevel = (type == POSITION_TYPE_BUY) ? avgPrice + PyramidFactor * g_atr * g_UnitsOpened
                                                     : avgPrice - PyramidFactor * g_atr * g_UnitsOpened;
      if( (type==POSITION_TYPE_BUY && priceNow >= nextLevel) ||
          (type==POSITION_TYPE_SELL && priceNow <= nextLevel))
      {
         double lots = CalcUnitLots(type == POSITION_TYPE_BUY);
         if(lots > 0)
         {
            (type==POSITION_TYPE_BUY) ? trade.Buy(lots, _Symbol) : trade.Sell(lots, _Symbol);
            g_UnitsOpened++;
         }
      }
   }

   // Stop-Loss nachziehen
   double newSL = (type==POSITION_TYPE_BUY) ? avgPrice - StopMul*g_atr : avgPrice + StopMul*g_atr;
   trade.PositionModify(_Symbol, newSL, 0);
}

//--- Neue Kerze erkennen & Hauptlogik
void OnTick()
{
   datetime curBar = iTime(_Symbol, TF, 0);
   if(curBar == lastBar) return;
   lastBar = curBar;

   if(!GetATR()) return;

   if(position.Select(_Symbol) && position.Volume() > 0)
   {
      ManagePosition();
      TryExit();
   }
   else
   {
      TryEntry();
   }
}
