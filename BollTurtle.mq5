//+------------------------------------------------------------------+
//|                                                   BollTurtle.mq5 |
//|                                                               CG |
//|                            Custom Bollinger/RSI Trading Strategy |
//+------------------------------------------------------------------+
#property copyright "CG"
#property link      ""
#property version   "1.00"

#property script_show_inputs
#include <Trade/Trade.mqh>

input int    BollPeriod      = 20;
input double BollDev         = 2.0;
input int    RSIPeriod       = 14;
input int    GlobalPeriod    = 50;
input double EntryThreshold  = 0.005; // 0.5% difference

CTrade trade;
double bollUpper[], bollMiddle[], bollLower[], rsi[];
double globalHigh = -DBL_MAX, globalLow = DBL_MAX;
bool inPosition = false;
ulong ticket = 0;

//+------------------------------------------------------------------+
int OnInit()
{
   SetIndexBuffer(0, bollUpper);
   SetIndexBuffer(1, bollMiddle);
   SetIndexBuffer(2, bollLower);
   SetIndexBuffer(3, rsi);
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
void OnTick()
{
   static datetime lastCheck = 0;
   MqlRates rates[];
   if(CopyRates(_Symbol, _Period, 0, BollPeriod+2, rates) < BollPeriod+2)
      return;

   if(rates[1].time == lastCheck) return;
   lastCheck = rates[1].time;

   double bb_up, bb_mid, bb_low;
   bb_mid = iMA(_Symbol, _Period, BollPeriod, 0, MODE_SMA, PRICE_CLOSE, 1);
   bb_up = bb_mid + BollDev * iStdDev(_Symbol, _Period, BollPeriod, 0, MODE_SMA, PRICE_CLOSE, 1);
   bb_low = bb_mid - BollDev * iStdDev(_Symbol, _Period, BollPeriod, 0, MODE_SMA, PRICE_CLOSE, 1);

   double rsiVal = iRSI(_Symbol, _Period, RSIPeriod, PRICE_CLOSE, 1);
   
   // Global high/low
   globalHigh = -DBL_MAX; globalLow = DBL_MAX;
   for(int i = 1; i <= GlobalPeriod; i++) {
      globalHigh = MathMax(globalHigh, rates[i].high);
      globalLow = MathMin(globalLow, rates[i].low);
   }

   double close0 = rates[0].close;
   double close1 = rates[1].close;

   bool prevOutside = (close1 > bb_up || close1 < bb_low);
   bool currInside = (close0 <= bb_up && close0 >= bb_low);
   bool currFurtherOutside = (close0 > bb_up || close0 < bb_low);
   
   if(prevOutside)
   {
      if(currInside && (rsiVal > 70 || rsiVal < 30))
         InsideTrade(close0, bb_mid, rsiVal);
      else if(currFurtherOutside)
      {
         if(close0 > bb_up && close0 > globalHigh)
            OutsideTrade(ORDER_TYPE_BUY, close0);
         else if(close0 < bb_low && close0 < globalLow)
            OutsideTrade(ORDER_TYPE_SELL, close0);
      }
   }

   CheckExit(bb_mid);
}

//+------------------------------------------------------------------+
void InsideTrade(double price, double bb_mid, double rsiVal)
{
   double diff = MathAbs(price - bb_mid);
   double percent = diff / price;
   if(percent <= EntryThreshold)
      return;

   double sl, volume = AccountInfoDouble(ACCOUNT_BALANCE) * 0.5 / price;
   ENUM_ORDER_TYPE type;

   if(price < bb_mid && rsiVal < 30) // LONG
   {
      type = ORDER_TYPE_BUY;
      sl = price - diff;
   }
   else if(price > bb_mid && rsiVal > 70) // SHORT
   {
      type = ORDER_TYPE_SELL;
      sl = price + diff;
   }
   else return;

   if(trade.PositionOpen(_Symbol)) return;
   trade.PositionClose(_Symbol);
   trade.SetStopLoss(sl);
   trade.Buy(volume, _Symbol, price, sl, 0);
}

//+------------------------------------------------------------------+
void OutsideTrade(ENUM_ORDER_TYPE type, double price)
{
   if(trade.PositionOpen(_Symbol)) return;
   double volume = AccountInfoDouble(ACCOUNT_BALANCE) * 0.5 / price;
   trade.PositionClose(_Symbol);
   if(type == ORDER_TYPE_BUY)
      trade.Buy(volume, _Symbol, price, 0, 0);
   else
      trade.Sell(volume, _Symbol, price, 0, 0);
}

//+------------------------------------------------------------------+
void CheckExit(double bb_mid)
{
   if(!trade.PositionOpen(_Symbol)) return;

   double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

   // Inside Trade Exit
   if((type == POSITION_TYPE_BUY && price >= bb_mid) ||
      (type == POSITION_TYPE_SELL && price <= bb_mid))
   {
      trade.PositionClose(_Symbol);
      return;
   }

   // Outside Trade Exit
   MqlRates rates[];
   CopyRates(_Symbol, _Period, 0, 2, rates);
   double bb_up = iBands(_Symbol, _Period, BollPeriod, 0, BollDev, PRICE_CLOSE, MODE_UPPER, 0);
   double bb_low = iBands(_Symbol, _Period, BollPeriod, 0, BollDev, PRICE_CLOSE, MODE_LOWER, 0);
   if((rates[0].close <= bb_up && type == POSITION_TYPE_SELL) ||
      (rates[0].close >= bb_low && type == POSITION_TYPE_BUY))
   {
      trade.PositionClose(_Symbol);
   }
}
//+------------------------------------------------------------------+
